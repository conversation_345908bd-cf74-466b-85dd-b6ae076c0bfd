import { sub, add, isWithinInterval, format } from 'date-fns'
import { mkConfig, generateCsv, asString } from 'export-to-csv'
import { saveAs } from 'file-saver'
import type { Period, Range } from '~/types'

export const useReportsStore = defineStore('reportsStore', {
  persist: {
    pick: [],
    storage: window?.localStorage
  },
  state: () => ({
    periodType: 'daily' as Period,
    range: {
      start: sub(new Date(), { days: 14 }),
      end: new Date()
    } as Range,
    surveyFilters: {} as Record<string, any>,
    loadings: {} as Record<string, any>,
    errors: {} as Record<string, any>,

    sessionsData: [] as any[],
    unansweredReportsData: [] as any[],
    unansweredReportsDataTotal: 0,
    unansweredReportsPagination: {
      page: 1,
      pageCount: 10,
      total: 0,
      asc: false
    },
    unansweredReportFilter: {
      range: {
        start: sub(new Date(), { days: 14 }),
        end: new Date()
      },
      session_id: '',
      chat_id: '',
      context_type: '',
      analyzed_action: '',
      processed: ''
    } as Record<string, any>,
    surveyReportsData: [] as any[],
    selectedSurveyReportsData: [] as any[],
    surveyReportsDataTotal: 0,
    surveyReportsPagination: {
      page: 1,
      pageCount: 10,
      total: 0,
      asc: false
    },

    selectedCategories: [] as any[],

    responseRateData: [] as any[],

    reportSummary: {} as any,
    reportSummaryForCompare: {} as any
  }),
  getters: {
    rangeDaysDuration(): number {
      return this.range.end.getDate() - this.range.start.getDate() + 1
    },
    rangeForCompare(): Range {
      return {
        start: sub(this.range.start, { days: this.rangeDaysDuration }),
        end: sub(this.range.end, { days: this.rangeDaysDuration })
      }
    },
    selectedCategoriesIds(): number[] {
      const categoriesStore = useCategoriesStore()
      const { categories } = storeToRefs(categoriesStore)
      return categories.value
        .filter(row => this.selectedCategories.includes(row.category))
        .map(row => row.category_id)
    },
    categoriesLegends(): any[] {
      const categoriesStore = useCategoriesStore()
      const { categories } = storeToRefs(categoriesStore)
      return categories.value
        .filter((row) => {
          return (
            this.selectedCategories.includes(row.category)
            || !this.selectedCategories.length
          )
        })
        .map((row) => {
          return {
            name: row.category,
            id: row.category_id,
            color: `#${generateColorFromString(row.category)}`
          }
        })
    },
    from_date(): string {
      return formatDateTimeForAPI(this.range.start)
    },
    to_date(): string {
      return formatDateTimeForAPI(this.range.end)
    },
    xAxis(): string[] {
      let start = this.range.start
      const end = this.range.end
      const xAxis: string[] = []
      switch (this.periodType) {
        case 'daily':
          while (start <= end) {
            xAxis.push(formatDateTimeForAPI(start))
            start = add(start, { days: 1 })
          }
          return xAxis
        case 'weekly':
          // create a range of weeks
          while (start <= end) {
            xAxis.push(formatDateTimeForAPI(start))
            start = add(start, { weeks: 1 })
          }
          return xAxis
        case 'monthly':
          // create a range of months
          while (start <= end) {
            xAxis.push(formatDateTime(start.toString(), 'yyyy-MM'))
            start = add(start, { months: 1 })
          }
          return xAxis
        default:
          return []
      }
    },
    sessionsTotal(): number {
      return this.sessionsData.reduce((acc, row) => acc + row.count, 0)
    },
    sessionsChartDataset(): number[] {
      return this.xAxis.map((date) => {
        const row = this.sessionsData.filter((row: any) => {
          // return if date is between date_start and date_end
          const _date = new Date(date)
          const _date_start = new Date(row.date_start)
          const _date_end = new Date(row.date_end)
          switch (this.periodType) {
            case 'daily':
              return isWithinInterval(_date, {
                start: _date_start,
                end: _date_end
              })
            case 'weekly':
              return isWithinInterval(_date_start, {
                start: date,
                end: add(date, { days: 7 })
              })
            case 'monthly':
              return (
                format(_date, 'yyyy-MM') === format(_date_start, 'yyyy-MM')
              )
            default:
              return false
          }
        })
        const total = row.reduce((acc, row) => acc + row.count, 0)
        return total
      })
    },
    sessionsByCategoriesChartDataset(): number[] {
      return this.xAxis.map((date) => {
        const row = this.sessionsData.filter((row: any) => {
          // return if date is between date_start and date_end
          if (
            this.selectedCategoriesIds.length
            && !this.selectedCategoriesIds.includes(row.category_id)
          ) {
            return false
          }
          const _date = new Date(date)
          const _date_start = new Date(row.date_start)
          const _date_end = new Date(row.date_end)
          switch (this.periodType) {
            case 'daily':
              return isWithinInterval(_date, {
                start: _date_start,
                end: _date_end
              })
            case 'weekly':
              return isWithinInterval(_date_start, {
                start: date,
                end: add(date, { days: 7 })
              })
            case 'monthly':
              return (
                format(_date, 'yyyy-MM') === format(_date_start, 'yyyy-MM')
              )
            default:
              return false
          }
        })
        // group by category category_id
        const grouped = row.reduce((acc: any, row: any) => {
          if (!acc[row.category_id]) {
            acc[row.category_id] = 0
          }
          acc[row.category_id] += row.count
          return acc
        }, {})
        return grouped
      })
    },
    responseRateByCategoriesChartDataset(): any[] {
      return this.xAxis.map((date) => {
        const row = this.responseRateData.filter((row: any) => {
          // return if date is between date_start and date_end
          if (
            this.selectedCategoriesIds.length
            && !this.selectedCategoriesIds.includes(row.category_id)
          ) {
            return false
          }
          const _date = new Date(date)
          const _date_start = new Date(row.date_start)
          const _date_end = new Date(row.date_end)
          switch (this.periodType) {
            case 'daily':
              return isWithinInterval(_date, {
                start: _date_start,
                end: _date_end
              })
            case 'weekly':
              return isWithinInterval(_date_start, {
                start: date,
                end: add(date, { days: 7 })
              })
            case 'monthly':
              return (
                format(_date, 'yyyy-MM') === format(_date_start, 'yyyy-MM')
              )
            default:
              return false
          }
        })
        // group by category category_id
        const grouped = row.reduce((acc: any, row: any) => {
          if (!acc[row.category_id]) {
            acc[row.category_id] = {
              total: 0,
              success: 0
            }
          }
          acc[row.category_id].total += row.total
          acc[row.category_id].success += row.success
          return acc
        }, {})
        return grouped
      })
    },
    sessionsTotalOfSelectedCategories(): number {
      let total = 0
      this.sessionsByCategoriesChartDataset.forEach((row: any) => {
        if (this.selectedCategoriesIds.length) {
          this.selectedCategoriesIds.forEach((id: number) => {
            total += row[id] || 0
          })
        } else {
          Object.values(row).forEach((value: number) => {
            total += value
          })
        }
      })
      return total
    },
    totalUnansweredReports(): number {
      return this.unansweredReportsData.length
    },
    responseRateSummary(): any {
      const summary = this.responseRateByCategoriesChartDataset.reduce(
        (acc, row) => {
          if (!acc) {
            acc = { total: 0, success: 0 }
          }
          Object.values(row).forEach((value: any) => {
            acc.total += value.total
            acc.success += value.success
          })
          return acc
        },
        { total: 0, success: 0 }
      )
      return summary
    },
    responseSuccessRateByCategoriesChartDataset(): number[] {
      return this.xAxis.map((date) => {
        const row = this.responseRateData.filter((row: any) => {
          // return if date is between date_start and date_end
          if (
            this.selectedCategoriesIds.length
            && !this.selectedCategoriesIds.includes(row.category_id)
          ) {
            return false
          }
          const _date = new Date(date)
          const _date_start = new Date(row.date_start)
          const _date_end = new Date(row.date_end)
          switch (this.periodType) {
            case 'daily':
              return isWithinInterval(_date, {
                start: _date_start,
                end: _date_end
              })
            case 'weekly':
              return isWithinInterval(_date_start, {
                start: date,
                end: add(date, { days: 7 })
              })
            case 'monthly':
              return (
                format(_date, 'yyyy-MM') === format(_date_start, 'yyyy-MM')
              )
            default:
              return false
          }
        })
        // group by category category_id
        const grouped = row.reduce((acc: any, row: any) => {
          if (!acc[row.category_id]) {
            acc[row.category_id] = 0
          }
          acc[row.category_id] += row.success
          return acc
        }, {})
        return grouped
      })
    },
    satisfiedTotal(): number {
      // count all row if score is 3 or 4
      return this.surveyReportsData.reduce((acc, row) => {
        if (row.score === 3 || row.score === 4) {
          return acc + 1
        }
        return acc
      }, 0)
    },
    unsatisfiedTotal(): number {
      // count all row if score is 0 or 1
      return this.surveyReportsData.reduce((acc, row) => {
        if (row.score === 0 || row.score === 1) {
          return acc + 1
        }
        return acc
      }, 0)
    },
    neutralTotal(): number {
      // count all row if score is 2
      return this.surveyReportsData.reduce((acc, row) => {
        if (row.score === 2) {
          return acc + 1
        }
        return acc
      }, 0)
    },
    surveyTotal(): number {
      return this.surveyReportsData.length
    },
    surveyReportsChartDataset(): any[] {
      const settingsSurveyStore = useSettingsSurveyStore()
      const { settingsSurveyOptions } = storeToRefs(settingsSurveyStore)
      const colors = ['green', 'yellow', 'red', 'blue', 'purple']
      // group and count by score
      const grouped = this.surveyReportsData.reduce((acc, row) => {
        if (!acc[row.score]) {
          acc[row.score] = 0
        }
        acc[row.score] += 1
        return acc
      }, {})
      return Object.keys(grouped).map((key, index) => {
        const surveyOption = settingsSurveyOptions.value.find(
          row => row.value === parseInt(key)
        )
        return {
          name: `${surveyOption?.text || key}`,
          value: grouped[key],
          color: colors[index]
        }
      })
    },
    hasAnyUnansweredReportFilter() {
      return (
        this.unansweredReportFilter.session_id
        || this.unansweredReportFilter.chat_id
        || this.unansweredReportFilter.context_type
        || this.unansweredReportFilter.analyzed_action
        || this.unansweredReportFilter.processed !== ''
      )
    }
  },
  actions: {
    async getDailySessions() {
      const { selectedEnvId, selectedEnv } = useApp()
      try {
        this.loadings.getDailySessions = true
        this.errors.getDailySessions = null
        const response = await useAPI().reportService.get(
          '/v1/report/sessions/byCategories/daily',
          {
            params: {
              from_date: this.from_date,
              to_date: this.to_date,
              // env_id: selectedEnvId.value,
              production_only: selectedEnv.value?.environment === 1 // 1 is production
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.sessionsData = response.data?.result
        return true
      } catch (error: any) {
        this.errors.getDailySessions = error?.response?.data || error
        return false
      } finally {
        this.loadings.getDailySessions = false
      }
    },
    async getDailyResponseRate() {
      const { selectedEnvId, selectedEnv } = useApp()
      try {
        this.loadings.getDailyResponseRate = true
        this.errors.getDailyResponseRate = null
        const response = await useAPI().reportService.get(
          '/v1/report/responseRate/daily',
          {
            params: {
              from_date: this.from_date,
              to_date: this.to_date,
              // env_id: selectedEnvId.value,
              production_only: selectedEnv.value?.environment === 1 // 1 is production
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.responseRateData = response.data?.result
        return true
      } catch (error: any) {
        this.errors.getDailyResponseRate = error?.response?.data || error
        return false
      } finally {
        this.loadings.getDailyResponseRate = false
      }
    },
    async getUnansweredReport() {
      const { selectedEnvId, selectedEnv } = useApp()
      try {
        this.loadings.getUnansweredReport = true
        this.errors.getUnansweredReport = null
        this.unansweredReportsData = []
        this.unansweredReportsDataTotal = 0
        const params: Record<string, any> = {
          page: this.unansweredReportsPagination.page,
          page_size: this.unansweredReportsPagination.pageCount,
          order: this.unansweredReportsPagination.asc,
          from_date: this.from_date,
          to_date: this.to_date,
          // env_id: selectedEnvId.value,
          production_only: selectedEnv.value?.environment === 1 // 1 is production
        }
        if (this.unansweredReportFilter.session_id) {
          params.session_id = this.unansweredReportFilter.session_id.trim()
        }
        if (this.unansweredReportFilter.chat_id) {
          params.chat_id = this.unansweredReportFilter.chat_id.trim()
        }
        console.log(
          '🚀 ~ getUnansweredReport ~ this.unansweredReportFilter:',
          this.unansweredReportFilter
        )

        if (this.unansweredReportFilter.context_type) {
          params.context_type = this.unansweredReportFilter.context_type
        }
        if (this.unansweredReportFilter.analyzed_action) {
          params.analyzed_action = this.unansweredReportFilter.analyzed_action
        }
        if (this.unansweredReportFilter.processed !== '') {
          params.processed = this.unansweredReportFilter.processed
        }
        const response = await useAPI().reportService.get(
          '/v1/report/unanswered/pagination',
          {
            params,
            paramsSerializer: {
              indexes: null
            }
          }
        )
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.unansweredReportsDataTotal = pagination?.total_count
        this.unansweredReportsData = response.data?.result
        return true
      } catch (error: any) {
        this.errors.getUnansweredReport = error?.response?.data || error
        return false
      } finally {
        this.loadings.getUnansweredReport = false
      }
    },

    async getSurveyReport() {
      const { selectedEnvId, selectedEnv } = useApp()
      try {
        this.loadings.getSurveyReport = true
        this.errors.getSurveyReport = null
        this.unansweredReportsData = []
        const response = await useAPI().reportService.get(
          '/v1/report/survey/pagination',
          {
            params: {
              score: this.surveyFilters.score?.value ?? null,
              from_date: this.from_date,
              to_date: this.to_date,
              // env_id: selectedEnvId.value,
              production_only: selectedEnv.value?.environment === 1, // 1 is production
              page: this.surveyReportsPagination.page,
              page_size: this.surveyReportsPagination.pageCount,
              order: this.surveyReportsPagination.asc
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.surveyReportsData = response.data?.result
        const pagination = tryParseJson(response?.headers['x-pagination'])
        this.surveyReportsDataTotal = pagination?.total_count
        return true
      } catch (error: any) {
        this.errors.getSurveyReport = error?.response?.data || error
        return false
      } finally {
        this.loadings.getSurveyReport = false
      }
    },

    exportUnansweredQuestionsToCSV(rows: any[], documentName?: string) {
      rows = rows.map(row => ({
        ...row,
        label: row?.label?.join(';')
      }))
      const config = mkConfig({ useKeysAsHeaders: true })
      const csvOutput = generateCsv(config)(rows)
      const blob = new Blob([csvOutput], {
        type: 'text/csv;charset=utf-8'
      })
      saveAs(blob, `${documentName || 'unanswered-questions'}.csv`)
    },

    async getReportSummary() {
      const { selectedEnvId, selectedEnv } = useApp()
      try {
        this.loadings.getReportSummary = true
        this.errors.getReportSummary = null
        this.reportSummary = {}
        const response = await useAPI().reportService.get(
          '/v1/report/summary',
          {
            params: {
              from_date: this.from_date,
              to_date: this.to_date,
              // env_id: selectedEnvId.value,
              production_only: selectedEnv.value?.environment === 1 // 1 is production
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.reportSummary = response.data?.result
        return true
      } catch (error: any) {
        this.errors.getReportSummary = error?.response?.data || error
        return false
      } finally {
        this.loadings.getReportSummary = false
      }
    },

    async getReportSummaryForCompare() {
      const { selectedEnvId, selectedEnv } = useApp()
      try {
        this.loadings.getReportSummaryForCompare = true
        this.errors.getReportSummaryForCompare = null
        this.reportSummaryForCompare = {}
        const response = await useAPI().reportService.get(
          '/v1/report/summary',
          {
            params: {
              from_date: formatDateTimeForAPI(this.rangeForCompare.start),
              to_date: formatDateTimeForAPI(this.rangeForCompare.end),
              // env_id: selectedEnvId.value,
              production_only: selectedEnv.value?.environment === 1 // 1 is production
            },
            paramsSerializer: {
              indexes: null
            }
          }
        )
        this.reportSummaryForCompare = response.data?.result
        return true
      } catch (error: any) {
        this.errors.getReportSummaryForCompare = error?.response?.data || error
        return false
      } finally {
        this.loadings.getReportSummaryForCompare = false
      }
    }
  }
})
